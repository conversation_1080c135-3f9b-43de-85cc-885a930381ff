/*==================== GOOGLE FONTS ====================*/
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap");

/*==================== VARIABLES CSS ====================*/
:root {
    
     --header-height: 3rem;
    /*========== Colors ==========*/
    /* Change favorite color */
    --hue-color: 250; /*Purple 250 - Green 142 - Blue 230 - Pink 340*/

    /* HSL color mode */
    --first-color: hsl(var(--hue-color), 69%, 61%);
    --first-color-second: hsl(var(--hue-color), 69%, 61%);
    --first-color-alt: hsl(var(--hue-color), 57%, 53%);
    --first-color-lighter: hsl(var(--hue-color), 92%, 85%);
    --title-color: hsl(var(--hue-color), 8%, 15%);
    --text-color: hsl(var(--hue-color), 8%, 45%);
    --text-color-light: hsl(var(--hue-color), 8%, 65%);
    --input-color: hsl(var(--hue-color), 70%, 96%);
    --body-color: hsl(var(--hue-color), 60%, 99%);
    --container-color: #fff;
    --scroll-bar-color: hsl(var(--hue-color), 12%, 90%);
    --scroll-thumb-color: hsl(var(--hue-color), 12%, 80%);

    /*========== Font and typography ==========*/
    --body-font: 'Poppins', sans-serif;

    /* .5rem = 8px, 1rem = 16px, 1.5rem = 24px ... */
    --big-font-size: 2rem;
    --h1-font-size: 1.5rem;
    --h2-font-size: 1.25rem;
    --h3-font-size: 1.125rem;
    --normal-font-size: .938rem;
    --small-font-size: .813rem;
    --smaller-font-size: .75rem;

    /*========== Font weight ==========*/
    --font-medium: 500;
    --font-semi-bold: 600;

    /*========== Margenes Bottom ==========*/
    /* .25rem = 4px, .5rem = 8px, .75rem = 12px ... */
    --mb-0-25: .25rem;
    --mb-0-5: .5rem;
    --mb-0-75: .75rem;
    --mb-1: 1rem;
    --mb-1-5: 1.5rem;
    --mb-2: 2rem;
    --mb-2-5: 2.5rem;
    --mb-3: 3rem;

    /*========== z index ==========*/
    --z-tooltip: 10;
    --z-fixed: 100;
    --z-modal: 1000;
}

/* Font size for large devices */
@media screen and (min-width: 968px) {
    :root {
        --big-font-size: 3rem;
        --h1-font-size: 2.25rem;
        --h2-font-size: 1.5rem;
        --h3-font-size: 1.25rem;
        --normal-font-size: 1rem;
        --small-font-size: .875rem;
        --smaller-font-size: .813rem;
    }
}

/*========== Variables Dark theme ==========*/
body.dark-theme{
  --first-color-dark: hsl(var(--hue-color), 8%, 20%);
  --first-color-second: hsl(var(--hue-color), 30%, 8%);
  --title-color: hsl(var(--hue-color), 4%, 95%);
  --text-color: hsl(var(--hue-color), 4%, 75%);
  --body-color: hsl(var(--hue-color), 28%, 12%);
  --container-color: hsl(var(--hue-color), 8%, 6%);
  --scroll-bar-color: hsl(var(--hue-color), 12%, 48%);
  --scroll-thumb-color: hsl(var(--hue-color), 12%, 36%);
  --box-shadow: 2px 2px 18px rgb(hsl(var(--input-color)));
  --input-color: hsl(var(--hue-color), 29%, 16%);
}

/*========== Button Dark/Light ==========*/
.change_theme{
  color: var(--title-color);
  font-size: 1.15rem;
  cursor: pointer;
}

.nav_btns{
  display: inline-flex;
  align-items: center;
  column-gap: 1rem;
}

/*==================== BASE ====================*/
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
  background-color: var(--body-color);
  color: var(--text-color);
}

h1, h2, h3, h4 {
  color: var(--title-color);
  font-weight: var(--font-semi-bold);
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
}

img {
  max-width: 100%;
  height: auto;
}

/*==================== REUSABLE CSS CLASSES ====================*/
.section {
  padding: 2rem 0 4rem;
}

.section__title {
  font-size: var(--h1-font-size);
  color: var(--title-color);
}

.section__subtitle {
  display: block;
  font-size: var(--small-font-size);
  margin-bottom: var(--mb-3);
}

.section__title, 
.section__subtitle {
  text-align: center;
}

/*==================== LAYOUT ====================*/
.container {
  max-width: 968px;
  margin-left: var(--mb-1-5);
  margin-right: var(--mb-1-5);
}

.grid {
  display: grid;
  gap: 1.5rem;
}

.header{
  width: 100%;
  background-color: var(--body-color);
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-fixed);
  transition: .4s; /*For animation dark mode*/
}

/*==================== NAV ====================*/
.nav{
  height: 3rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav_logo,
.nav_toggle,
.nav_close{
  color: var(--title-color);
}

.nav_logo{
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: -1px;
  display: inline-flex;
  align-items: center;
  column-gap: .5rem;
  transition: .3s;
}

.nav_logo:hover{
  color: var(--first-color);
}

.nav_toggle{
  display: inline-flex;
  font-size: 1.25rem;
  cursor: pointer;
}

@media screen and (max-width: 767px){
  .nav_menu{
    position: fixed;
    background-color: var(--container-color);
    width: 80%;
    height: 100%;
    top: 0;
    right: -100%;
    box-shadow: -2px 0 4px hsla(var(--hue-color), 24%, 15%, .1) ;
    padding: 4rem 0 0 3rem;
    border-radius: 1rem 0 0 1rem;
    transition: .3s;
    z-index: var(--z-fixed);
  }
}

.nav_close{
  font-size: 1.5rem;
  position: absolute;
  top: 1rem;
  right: 1.25rem;
  cursor: pointer;
}

.nav_list{
  display: flex;
  flex-direction: column;
  row-gap: 1.5rem;
}

.nav_link{
  color: var(--title-color);
  font-weight: var(--font-medium);
  transition: .3s;
}

.nav_link:hover{
  color: var(--first-color);
}

/* show menu */
.show_menu{
  right: 0;
}

/* Active link */
.active_link{
  position: relative;
  color: var(--first-color);
}

.active_link::after{
  content: '';
  position: absolute;
  bottom: -.5rem;
  left: 0;
  width: 50%;
  height: 2px;
  background-color: var(--first-color);
}

/* Change background header */
.scroll_header{
  box-shadow: 0 1px 4px hsla(var(--hue-color), 4%, 15%, .1);
}

/*==================== HOME ====================*/
.home{
  padding: 6.5rem 0 2rem;
}

.home_container{
  position: relative;
  row-gap: 2rem;
}

.home_img{
  justify-self: center;
  background-color: var(--first-color);
  width: 200px;
  height: 200px;
  border-radius: 50%;
  padding-left: 40px;
  overflow: hidden;
  margin-bottom: var(--mb-1);
}

.home_title{
  font-size: var(--big-font-size);
  line-height: 104%;
  margin-bottom: var(--mb-0-75);
}

.home_subtitle{
  font-size: var(--h3-font-size);
  color: var(--text-color);
  font-weight: var(--font-medium);
  margin-bottom: var(--mb-0-75);
}

.home_description{
  margin-bottom: var(--mb-2);
}

.home_scroll{
  padding-top: var(--mb-0-75);
}

.home_scroll-button{
  color: var(--first-color);
  transition: .3s;
}

.home_scroll-button:hover{
  transform: translateY(.25rem)
}

.home_scroll-mouse{
  font-size: 2rem;
}

.home_scroll-name{
  font-size: var(--small-font-size);
  color: var(--title-color);
  font-weight: var(--font-medium);
  margin-right: var(--mb-0-25);
  cursor: pointer;
}

.home_scroll-arrow{
  font-size: 1.25rem;
}

.home_social{
  position: absolute;
  top: 2rem;
  right: .1rem;
  display:grid;
  justify-items: center;
  row-gap: 3.5rem;
}

.home_social-follow{
  font-weight: var(--font-medium);
  font-size: var(--smaller-font-size);
  color: var(--first-color);
  position: relative;
  transform: rotate(90deg);
}

.home_social_follow::after{
  content: '';
  position: absolute;
  width: 1rem;
  height: 2px;
  background-color: var(--first-color);
  right: -45%;
  top: 50%;
}

.home_social-links{
  display: inline-flex;
  flex-direction: column;
  row-gap: .25rem;
}

.home_social-icon{
  font-size: 1.25rem;
  color: var(--first-color);
}

.home_social-icon:hover{
  color: var(--first-color);
}




/*==================== BUTTONS ====================*/
.button{
  display: inline-block;
  background-color: var(--first-color);
  color: #FFF;
  padding: 1rem;
  border-radius: .5rem;
  font-weight: var(--font-medium);
  cursor: pointer;
}

.button:hover{
  background-color: var(--first-color-alt);
}

.button_icon{
  font-style: 1.25rem;
  margin-left: var(--mb-0-5);
  transition: .3s;
}

.button--white{
  background-color: #FFF;
  color: var(--first-color);
}

.button--white:hover{
  background-color: #FFF;
}

.button--flex{
  display: inline-flex;
  align-items: center;
}

.button--small{
  padding: .75rem 1rem;
}

.button--link{
  padding: 0;
  background-color: transparent;
  color: var(--first-color);
}

.button--link:hover{
  background-color: transparent;
  color: var(--first-color-alt);
}

/*==================== ABOUT ====================*/
.about_img{
  width: 200px;
  border-radius: .5rem;
  justify-self: center;
  align-self: center;
}

.about_description{
  text-align: center;
  margin-bottom: var(--mb-2-5);
}

.about_info{
  display: flex;
  justify-content: space-evenly;
  margin-bottom: var(--mb-2-5);
}

.about_info-title{
  font-size: var(--h2-font-size);
  font-weight: var(--font-semi-bold);
  color: var(--title-color);
}

.about_info-name{
  font-size: var(--smaller-font-size);
}

.about_info-title,
.about_info-name{
  display: block;
  text-align: center;
}

.about_buttons{
  display: flex;
  justify-content: center;
}

/*==================== SKILLS ====================*/
.skills_container{
  row-gap: 0;
}

.skills_header{
  display: flex;
  align-items: center;
  margin-bottom: var(--mb-2-5);
  cursor: pointer;
}

.skills_icon,
.skills_arrow{
  font-size: 2rem;
  color: var(--first-color);
}

.skills_icon{
  margin-right: var(--mb-0-75);
}

.skills_title{
  font-size: var(--h3-font-size);
}

.skills_subtitle{
  font-size: var(--small-font-size);
  color: var(--text-color-light);
}

.skills_list{
  row-gap: 1.5rem;
  padding-left: 2.7rem;
}

.skills_arrow{
  margin-left: auto;
  transition: .4s;
}

.skills_titles{
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--mb-0-5);
}

.skills_name{
  font-size: var(--normal-font-size);
  font-weight: var(--font-medium);
}

.skills_bar,
.skills_percentage{
  height: 5px;
  border-radius: .25rem;
}

.skills_bar{
  background-color: var(--first-color-lighter);
}

.skills_percentage{
  display: block;
  background-color: var(--first-color);
}

.skills_html{
  width: 90%;
}

.skills_css{
  width: 80%;
}

.skills_javascript{
  width: 60%;
}

.skills_php{
  width: 80%;
}

.skills_nodejs{
  width: 60%;
}
.skills_python{
  width: 70%;
}
.skills_ruby{
  width: 55%;
}

.skills_figma{
  width: 90%;
}

.skills_sketch{
  width: 85%;
}

.skills_adobexd{
  width: 80%;
}

.skills_photoshop{
  width: 85%;
}

.skills_close .skills_list{
  height: 0;
  overflow: hidden;
}

.skills_open .skills_list{
  height: max-content;
  margin-bottom: var(--mb-2-5);
}

.skills_open .skills_arrow{
  transform: rotate(-180deg);
}
/*==================== QUALIFICATION ====================*/
.qualification_tabs{
  display: flex;
  justify-content: space-evenly;
  margin-bottom: var(--mb-2);
  gap: 20px;
    margin-left: -60px;
    cursor: pointer;
}

.qualification_button{
  font-size: var(--h3-font-size);
  cursor: pointer;
}

.qualification_button:hover{
  color: var(--first-color);
  cursor: pointer;
}

.qualification_icon{
  font-size: 1.8rem;
  margin-right: var(--mb-0-25);
}

.qualification_data{
  display: grid;
  grid-template-columns: 1fr max-content 1fr;
  column-gap: 1.5rem;
}

.qualification_title{
  font-size: var(--normal-font-size);
  font-weight: var(--font-medium);
}

.qualification_subtitle{
  display: inline-block;
  font-size: var(--small-font-size);
  margin-bottom: var(--mb-1);
}

.qualification_calendar{
  font-size: var(--smaller-font-size);
  color: var(--text-color-light);
}

.qualification_rounder{
  display: inline-block;
  width: 13px;
  height: 13px;
  background-color: var(--first-color);
  border-radius: 50%;
}

.qualification_line{
  display: block;
  width: 1px;
  height: 100%;
  background-color: var(--first-color);
  transform: translate(6px, -7px);
}

.qualification [data-content]{
  display: none;
}

.qualification_active[data-content]{
  display: block;
}

.qualification_button.qualification_active{
  color: var(--first-color);
}
/*==================== SERVICES ====================*/
.services_container{
  gap: 1.5rem;
  grid-template-columns: repeat(2, 1fr);
}

.services_content{
  position: relative;
  background-color: var(--container-color);
  padding: 3.5rem .5rem 1.25rem 1.5rem;
  border-radius: .25rem;
  box-shadow: 0 2px 4px rbba(0,0,0,.15);
  transition: .3s;
}

.services_content:hover{
  box-shadow: 0 4px 8px rgba(0,0,0,.15);
}

.services_icon{
  display: block;
  font-size: 1.5rem;
  color: var(--first-color);
  margin-bottom: var(--mb-1);
}

.services_title{
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-1);
  font-weight: var(--font-medium);
}

.services_button:hover .button_icon{
  transform: translateX(.25rem)
}

.services_modal{
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,.5);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1rem;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: .3s;
}

.services_modal-content{
  position:relative;
  background-color: var(--container-color);
  padding: 1.5rem;
  border-radius: .5rem;
}

.services_modal-services{
  row-gap: 1rem;
}

.services_modal-service{
  display: flex;
}

.services_modal-title{
  font-size: var(--h3-font-size);
  font-weight: var(--font-medium);
  margin-bottom: var(--mb-1-5);
}

.services_modal-close{
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  color: var(--first-color);
  cursor: pointer;
}

.services_modal-icon{
  color: var(--first-color);
  margin-right: var(--mb-0-25);
}

.active_modal{
  opacity: 1;
  visibility: visible;
}
/* Active Modal */


/*==================== PORTFOLIO ====================*/
.portfolio_container{
  overflow: initial;
}

.portfolio_content{
  padding: 0 1.5rem;
}

.portfolio_img{
  width: 265px;
  border-radius: .5rem;
  justify-self: center;
}

.portfolio_title{
  font-size: var(--h3-font-size);
  margin-bottom: var(--mb-0-5);
}

.portfolio_description{
  margin-bottom: var(--mb-0-75);
}

.portfolio_button{
  transform: translateX(.25rem)
}

.swiper-button-prev::after,
.swiper-button-next::after{
  content: '';
}

.swiper-portfolio-icon{
  font-size: 2rem;
  color: var(--first-color);
}

.swiper-button-prev{
  left: -.5rem;
}

.swiper-button-next{
  right: -.5rem;
}

.swiper-container-horizontal > .swiper-pagination-bullets{
  bottom: -2.5rem;
}

.swiper-pagination-bullet-active{
  background-color: var(--first-color);
}

.swiper-button-prev,
.swiper-button-next,
.swiper-pagination-bullet{
  outline: none;
}

/*==================== PROJECT IN MIND ====================*/
.project{
  text-align: center;
}

.project_bg{
  background-color: var(--first-color-second);
  padding-top: 3rem;
}

.project_title{
  font-size: var(--h2-font-size);
  margin-bottom: var(--mb-0-75);
}

.project_description{
  margin-bottom: var(--mb-1-5);
}

.project_title,
.project_description{
  color: #FFF;
}

.project_img{
  width: 232px;
  justify-self: center;
}
/*==================== TESTIMONIAL ====================*/
.testimonial_data,
.testimonial_header{
  display: flex;
}

.testimonial_data{
  justify-content: space-between;
  margin-bottom: var(--mb-1);
}

.testimonial_img{
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: var(--mb-0-75);
}

.testimonial_name{
  font-size: var(--h3-font-size);
  font-weight: var(--font-medium);
}

.testimonial_client{
  font-size: var(--small-font-size);
  color: var(--text-color-light);
}

.testimonial_description{
  margin-bottom: var(--mb-2-5);
}

.testimonial_icon-star{
  color: var(--first-color);
}

.swiper-container .swiper-pagination-testimonial{
  bottom: 0;
}

/*==================== CONTACT ME ====================*/
.contact_container{
  row-gap: 3rem;
}

.contact_information{
  display: flex;
  margin-bottom: var(--mb-2);
}

.contact_icon{
  font-size: 2rem;
  color: var(--first-color);
  margin-right: var(--mb-0-75);
}

.contact_title{
  font-size: var(--h3-font-size);
  font-weight: var(--font-medium);
}

.contact_subtitle{
  font-size: var(--small-font-size);
  color: var(--text-color-light);
}

.contact_content{
  background-color: var(--input-color);
  border-radius: .5rem;
  padding: .75rem 1rem .25rem;
}

.contact_label{
  font-size: var(--small-font-size);
  color: var(--title-color);
}

.contact_input{
  width: 100%;
  background-color: var(--input-color);
  color: var(--text-color);
  font-family: var(--body-color);
  font-size: var(--normal-font-size);
  border: none;
  outline: none;
  padding: .25rem .5rem .5rem 0;
}

/*==================== FOOTER ====================*/
.footer{
  padding-top: 2rem;
}

.footer_container{
  row-gap: 3.5rem;
}
.footer_bg{
  background-color: var(--first-color-second);
  padding: 2rem 0 3rem;
}

.footer_title{
  font-size: var(--h1-font-size);
  margin-bottom: var(--mb-0-25);
}

.footer_subtitle{
  font-size: var(--small-font-size);
}

.footer_links{
  display: flex;
  flex-direction: column;
  row-gap: 1.5rem;
}

.footer_link:hover{
  color: --first-color-lighter
}

.footer_social{
  font-size: 1.25rem;
  margin-right: var(--mb-1-5);
}

.footer_social:hover{
  color: var(--first-color-lighter);
}

.footer_copy{
  font-size: var(--small-font-size);
  text-align: center;
  color: var(--text-color-light);
  margin-top: var(--mb-3);
}

.footer_title,
.footer_subtitle,
.footer_link,
.footer_social{
  color: #FFF;
}
/*========== SCROLL UP ==========*/
.scrollup{
  position: fixed;
  right: 1rem;
  bottom: -20%;
  background-color: var(--first-color);
  opacity: .8;
  padding: 0 .3rem;
  border-radius: .4rem;
  z-index: var(--z-tooltip);
  transition: .4s;
}

.scrollup:hover{
  background-color: var(--first-color-alt);
}

.scrollup_icon{
  font-size: 1.5rem;
  color: #FFF;
}

/* Show scroll */
.show_scroll{
  bottom: 5rem;
}

/*========== SCROLL BAR ==========*/
::-webkit-scrollbar{
  width: .60rem;
  background-color: var(--scroll-bar-color);
  border-radius: .5rem;
}

::-webkit-scrollbar-thumb{
  background-color: var(--scroll-thumb-color);
  border-radius: .5rem;
}

::-webkit-scrollbar-thumb:hover{
  background-color: var(--text-color-light);
}

/*==================== MEDIA QUERIES ====================*/
/* For small devices */
@media screen and (max-width: 350px){
  .container{
    margin-left: var(--mb-1);
    margin-right: var(--mb-1);
  }

  .nav_menu{
    padding: 2rem .25rem 4rem;
  }

  .nav_list{
    column-gap: 0;
  }

  .home_content{
    grid-template-columns: .25fr 3fr;
  }

  .home_scroll{
    visibility: hidden;
  }

  .home_blog{
    width: 180px;
  }

  .skills_title{
    font-size: var(--normal-font-size);
  }

  .qualification_data{
    gap: .5rem;
  }

  .services_container{
    grid-template-columns: max-content;
    justify-content: center;
  }

  .services_content{
    padding-right: 3.5rem;
  }

  .services_modal{
    padding: 0 .5rem;
  }

  .project_img{
    width: 200px;
  }

  .testimonial_data,
  .testimonial_header{
    flex-direction: column;
    align-items: center;
  }

  .testimonial_img{
    margin-right: 0;
    margin-bottom: var(--mb-0-25);
  }

  .testimonial_data,
  .testimonial_description{
    text-align: center;
  }
}

/* For medium devices */
@media screen and (max-width:568px){
  .home_scroll_social{
    visibility: hidden;
  }
}

@media screen and (min-width:568px){
  .home_container{
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    padding-top: .5rem;
  }

  .home_data{
    justify-self: center;
    padding-top: 30px;
  }

  

  .home_img{
    justify-self: center;
    background-color: var(--first-color);
    width: 240px;
    height: 240px;
    border-radius: 50%;
    overflow: hidden;
    padding-left: 60px;
    order: 1;
  }

  .home_social a {
    display: inline-block;
    justify-content: center;
    text-align: center;
    padding: 5px 5px;
  }

  .home_social{
    top: 3rem;
    right: .2rem;
  }
  
  .home_scroll{
    padding-top: 0;
  }
  
  .home_scroll_social{
    visibility: hidden;
  }

  .home_content{
    grid-template-columns: max-content 1fr 1fr;
  }

  .home_data{
    grid-column: initial;
  }

  .about_container,
  .skills_container,
  .portfolio_content,
  .project_container,
  .contact_container,
  .footer_container{
    grid-template-columns: repeat(2, 1fr);
  }

  .qualification_sections{
    display: grid;
    grid-template-columns: .6fr;
    justify-content: center;
  }
}

@media screen and (min-width: 767px){
  body{
    margin: 0;
  }

  .nav{
    height: calc(var(--header-height) + 1.5rem);
    column-gap: 3rem;
  }
  .nav_toggle,
  .nav_close{
    display: none;
  }
  .nav_list{
    flex-direction: row;
    column-gap: 3rem;
  }
  .nav_menu{
    margin-left: auto;
  }

  .nav_toogle{
    visibility: hidden;
  }
}


/* For large devices */
@media screen and (min-width: 768px){
  .container{
    margin-left: auto;
    margin-right: auto;
  }

  body{
    margin: 0;
  }

  .section{
    padding: 6rem 0 2rem;
  }

  .section__subtitle{
    margin-bottom: 4rem;
  }

  .header{
    top: 0;
    bottom: initial;
  }

  .header,
  .main,
  .footer_container{
    padding: 0 1rem;
  }

  .nav{
    height: calc(var(--header-height) + 1.5rem);
    column-gap: 1rem;
  }

  .nav_icon,
  .nav_close,
  .nav_toggle{
    display: none;
  }

  .nav_list{
    display: flex;
    column-gap: 2rem
  }

  .nav_list{
    display:flex;
    column-gap: 2rem;
  }

  .nav_menu{
    margin-left: auto;
  }

  .nav_toogle{
    visibility: hidden;
  }

  .change_theme{
    margin: 0;
  }

  .home_container{
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    padding-top: 3.5rem;
  }

  .home_data{
    justify-self: center;
    padding-top: 50px;
  }

  .home_img{
    justify-self: center;
    background-color: var(--first-color);
    width: 340px;
    height: 340px;
    border-radius: 50%;
    overflow: hidden;
    padding-left: 80px;
    order: 1;
  }

  

  .home_social{
    visibility: hidden;
  }

  .home_scroll{
    visibility: hidden;
  }
  
  .home_scroll_social{
    visibility: visible;
    display: grid;
    grid-template-columns: 1fr 1fr;
    justify-content: start;
  }

  .home_social1 a {
    display: inline-block;
    justify-content: center;
    text-align: center;
    padding: 10px 10px;
    visibility: visible;
  }

  .qualification_tabs{
    justify-content: center;
  }

  .qualification_button{
    margin: 0 var(--mb-1);
  }

  .qualification_sections{
    grid-template-columns: .5fr;
  }

  .services_container{
    grid-template-columns: repeat(4,218px);
    justify-content: center;
  }

  .services_icon{
    font-size: 2rem;
  }
  
  .services_content{
    padding: 6rem 0 2rem 2.5rem;
  }

  .services_modal-content{
    width: 450px;
  }

  .portfolio_img{
    width: 320px;
  }

  .portfolio_content{
    align-items: center;
  }

  .project{
    text-align: initial;
  }

  .project_bg{
    background: none;
  }

  .project_container{
    background-color: var(--first-color-second);
    border-radius: 1rem;
    padding: 3rem 2.5rem 0;
    grid-template-columns: 1fr max-content;
    column-gap: 3rem;
  }

  .project_data{
    padding-top: .8rem;
  }

  .footer_container{
    grid-template-columns: repeat(3,1fr);
  }

  .footer_bg{
    padding: 3rem 0 3.5rem;
  }

  .footer_links{
    flex-direction: row;
    column-gap: 2rem;
  }

  .footer_social{
    justify-self: flex-end;
  }

  .footer_copy{
    margin-top: 4.5rem;
  }

  /*For x-large devices */
  @media screen and (min-width: 1024px){
    body{
      font-size: var(--font-medium);
    }

    .header,
    .main,
    .footer_container{
      padding: 0;
    }

    .home_img{
      width: 440px;
      height: 440px;
    }

    .nav_toogle{
      visibility: hidden;
    }

    .services_container{
      grid-template-columns: repeat(4, 238px);
    }

    .portfolio_content{
      column-gap: 5rem;
    }

    .swiper-portfolio-icon{
      font-size: 3.5rem;
    }

    .swiper-button-prev{
      left: -3.5rem;
    }

    .swiper-button-next{
      right: -3.5rem;
    }

    .swiper-container-horizontal > .swiper-pagination-bullets{
      bottom: -4.5rem;
    }
    
  }


}
