
<?php $__env->startSection('content'); ?>
    <section class="home section" id="home">
        <div class="home_container container grid">
            <?php $__currentLoopData = $abouts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $about): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="home_img">
                <?php if($about->home_image): ?>
                <img src="<?php echo e(asset('assets/img/' . $about->home_image)); ?>" alt="">
                <?php else: ?>
                <img src="<?php echo e(asset('assets/img/home.png')); ?>" alt="">
                <?php endif; ?>
            </div>

            <div class="home_data">
                <h1 class="home_title">Hi, I'am <?php echo e($about->name); ?></h1>
                <h3 class="home_subtitle"><?php echo $about->description; ?></h3>
                <p class="home_description">
                    <?php echo $about->tagline; ?>

                </p>

                <a href="#contact" class="button button--flex">
                    Contact Me <i class="uil uil-message button__icon"></i>
                </a>
                <div class="home_scroll">
                    <a href="#about" class="home_scroll-button button--flex"></a>
                    <i class="uil uil-mouse-alt home_scroll-mouse"></i>
                    <span class="home_scroll-name">Scroll down</span>
                    <i class="uil uil-arrow-down home_scroll-arrow"></i>
                </div>
                <div class="home_social">
                    <span class="home_social-follow">Follow Me</span>
                    <div class="home_social-links">
                        <?php $__currentLoopData = $mediasHome; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $media): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e($media->link); ?>" target="_blank" class="home_social-icon">
                            <i class="uil uil-<?php echo e($media->icon); ?>"></i>
                        </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <div class="home_scroll_social">
                    <div class="home_scroll1">
                        <a href="#about" class="home_scroll-button button--flex"></a>
                        <i class="uil uil-mouse-alt home_scroll-mouse"></i>
                        <span class="home_scroll-name">Scroll down</span>
                        <i class="uil uil-arrow-down home_scroll-arrow"></i>
                    </div>
                    <div class="home_social1">
                        <div class="home_social-link">
                            <?php $__currentLoopData = $mediasHome; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $media): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a href="<?php echo e($media->link); ?>" target="_blank" class="home_social-icon">
                                <i class="uil uil-<?php echo e($media->icon); ?>"></i>
                            </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            
        </div>
    </section>

    <!--==================== ABOUT ====================-->
    <section class="about section" id="about">
        <h2 class="section__title">About Me</h2>
        <span class="section__subtitle">My introduction</span>

        <div class="about_container container grid">
            <?php $__currentLoopData = $abouts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $about): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($about->banner_image): ?>
                    <img src="<?php echo e(asset('assets/img/' . $about->banner_image)); ?>" alt="" class="about_img">
                <?php else: ?>
                    <img src="<?php echo e(asset('assets/img/about-img.png')); ?>" alt="" class="about_img">
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <div class="about_data">
                <p class="about_data">
                    <?php echo $about->summary; ?>

                </p>
            <br>
            <br>

            <div class="about_info">
                <div>
                    <span class="about_info-title">10+</span>
                    <span class="about_info-name">Years <br>experience</span>
                </div>
                <div>
                    <span class="about_info-title"><?php echo e($projectCount); ?>+</span>
                    <span class="about_info-name">Completed <br>project</span>
                </div>
                <div>
                    <span class="about_info-title"><?php echo e($experiencesCount); ?>+</span>
                    <span class="about_info-name">Companies <br>worked</span>
                </div>
            </div>
            <div class="about_buttons">
                <a href="<?php echo e(asset('assets/pdf/johndoe-Cv.pdf')); ?>" class="button button--flex">
                    Download CV <i class="uil uil-download-alt button_icon"></i>
                </a>
            </div>
            </div>
        </div>
    </section>

    <!--==================== SKILLS ====================-->
    <section class="skills section" id="skills">
        <h2 class="section__title">Skills</h2>
        <span class="section__subtitle">My technical level</span>

        <div class="skills_container container grid">
            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div>
                <div class="skills_content skills_open">
                    <div class="skills_header">
                        <i class="uil <?php echo e($service->icon); ?> skills_icon"></i>

                        <div>
                            <h1 class="skills_title"><?php echo e($service->name); ?></h1>
                            <span class="skills_subtitle">More than <?php echo e($service->experience); ?> years</span>
                        </div>

                        <i class="uil uil-angle-down skills_arrow"></i>
                    </div>
                    <div class="skills_list grid">
                        <?php $__currentLoopData = $service->skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="skills_data">
                            <div class="skills_titles">
                                <h3 class="skills_name"><?php echo e($skill->name); ?></h3>
                                <span class="skills_number"><?php echo e($skill->proficiency); ?>%</span>
                            </div>
                            <div class="skills_bar">
                                <span class="skills_percentage" style="width: <?php echo e($skill->proficiency); ?>%;"></span>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div> 
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>            
        </div>
    </section>

    <!--==================== QUALIFICATION ====================-->
    <section class="qualification section">
        <h2 class="section__title">Qualification</h2>
        <span class="section__subtitle">My personal journal</span>

        <div class="qualification_container container">
        <div class="qualification_tabs">
            <div class="qualificaction_button button--flex qualification_active" data-target="#education">
                <i class="uil uil-graduation-cap qualification_icon"></i>
                Education
            </div>
            <div class="qualificaction_button button--flex" data-target="#work">
                <i class="uil uil-briefcase-alt qualification-icon"></i>
                Work
            </div>
        </div>

        <div class="qualification_sections">
            <!--========== QUALIFICATION CONTENT 1 ==========-->
            <div class="qualification_content qualification_active" data-content id="education">
                <?php $__currentLoopData = $educations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $education): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($loop->iteration % 5): ?>
                        <div class="qualification_data">
                            <div>
                                <h3 class="qualification_title"><?php echo e($education->degree); ?></h3>
                                <span class="qualification_subtitle"><?php echo e($education->institution); ?></span>
                                <span class="qualification_subtitle"><?php echo e($education->department); ?></span>
                                <div class="qualificaation_calender">
                                    <i class="uil uil-calender-alt"></i>
                                    <?php echo e($education->period); ?>

                                </div>
                            </div>
                            <div>
                                <span class="qualification_rounder"></span>
                                <span class="qualification_line"></span>
                            </div>               
                        </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <!--========== QUALIFICATION CONTENT 2 ==========-->
            <div class="qualification_content" data-content id="work">
                <?php $__currentLoopData = $experiences; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $experience): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($loop->iteration % 4): ?>
                        <div class="qualification_data">
                            <div>
                                <h3 class="qualification_title"><?php echo e($experience->position); ?></h3>
                                <span class="qualification_subtitle"><?php echo e($experience->company); ?></span>
                                <div class="qualificaation_calender">
                                    <i class="uil uil-calender-alt"></i>
                                    <?php echo e($experience->period); ?>

                                </div>
                            </div>
                            <div>
                                <span class="qualification_rounder"></span>
                                <span class="qualification_line"></span>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        </div>
    </section>

    <!--==================== SERVICES ====================-->
    <section class="services section" id="services">
        <h2 class="section__title">Services</h2> 
        <span class="section__subtitle">What is offer</span> 

        <div class="services_container container grid">
            <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="services_content">
                    <div>
                        <i class="uil <?php echo e($service->icon); ?> services_icon"></i>
                        <h3 class="services_title"><?php echo e($service->name); ?></h3>
                    </div>
                    <span class="button button--flex button--small button--link services_button">
                        View More
                        <i class="uil uil-arrow-right button_icon"></i>
                    </span>

                    <div class="services_modal ">
                        <div class="services_modal-content">
                            <h4 class="services_modal-title"><?php echo e($service->name); ?></h4>
                            <i class="uil uil-times services_modal-close"></i>

                            <ul class="services_modal-services grid">
                                <li class="services_modal-service">
                                    <i class="uil uil-check-circle services_modal-icon"></i>
                                    <p><?php echo e($service->description); ?></p>
                                </li>
                                <li class="services_modal-service">
                                    <i class="uil uil-check-circle services_modal-icon"></i>
                                    <p>Web page development.</p>
                                </li>
                                <li class="services_modal-service">
                                    <i class="uil uil-check-circle services_modal-icon"></i>
                                    <p>I create ux element interactions.</p>
                                </li>
                                <li class="services_modal-service">
                                    <i class="uil uil-check-circle services_modal-icon"></i>
                                    <p>I position your company brand.</p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </section>

    <!--==================== PORTFOLIO ====================-->
    <section class="portfolio section" id="portfolio">
        <h2 class="section__title">Portfolio</h2>  
        <span class="section__subtitle">Most recent work</span>

        <div class="portfolio_container container swiper-container">
        <div class="swiper-wrapper">
            <!--============ PORTFOLIO 1 ==============-->
            <div class="portfolio_content grid swiper-slide">
                <img src="<?php echo e(asset('assets/img/portfolio1.jpeg')); ?>" alt="" class="portfolio_img">

                <div class="portfolio_data">
                    <h3 class="portfolio_title">Modern Website</h3>
                    <p class="portfolio_description">
                        Website adaptable to all devices,with ui description
                        and animated interactions.
                    </p>
                    <a href="#" class="button button--flex button--small portfolio_button">
                        Demo 
                        <i class="uil uil-arrow-right button__icon"></i>
                    </a>
                </div>
            </div>
            <!--============ PORTFOLIO 2 ==============-->
            <div class="portfolio_content grid swiper-slide">
                <img src="<?php echo e(asset('assets/img/portfolio2.jpeg')); ?>" alt="" class="portfolio_img">

                <div class="portfolio_data">
                    <h3 class="portfolio_title">POS App</h3>
                    <p class="portfolio_description">
                        POS App description
                    </p>
                    <a href="#" class="button button--flex button--small portfolio_button">
                        Demo 
                        <i class="uil uil-arrow-right button__icon"></i>
                    </a>
                </div>
            </div>
            <!--============ PORTFOLIO 3 ==============-->
            <div class="portfolio_content grid swiper-slide">
                <img src="<?php echo e(asset('assets/img/portfolio3.jpeg')); ?>" alt="" class="portfolio_img">

                <div class="portfolio_data">
                    <h3 class="portfolio_title">Online Store</h3>
                    <p class="portfolio_description">
                        Website adaptable to all devices,with ui description
                        and animated interactions.
                    </p>
                    <a href="#" class="button button--flex button--small portfolio_button">
                        Demo 
                        <i class="uil uil-arrow-right button__icon"></i>
                    </a>
                </div>
            </div>
        </div>

        <!--Add Arrow-->
        <div class="swiper-button-next">
            <i class="uil uil-angle-right-b swiper-portfolio-icon"></i>
        </div>
        <div class="swiper-button-prev">
            <i class="uil uil-angle-left-b swiper-portfolio-icon"></i>
        </div>
        <!--Add Pagination-->
        <div class="swiper-pagination"></div>
        </div>
    </section>

    <!--==================== PROJECT IN MIND ====================-->
    <section class="project section">
        <div class="project_bg">
        <div class="project_container container grid">
            <div class="project_data">
                <h2 class="project_title">You have new project</h2>
                <p class="project_description">Contact me now and get a 50% discounted</p>
                <a href="#contact" class="button button--flex button--white">
                    Contact Me 
                    <i class="uil uil-message project_icon button_icon"></i>
                </a>
            </div>

            <img src="<?php echo e(asset('assets/img/home.png')); ?>" alt="" class="project_img">
        </div>
        </div>
    </section>

    <!--==================== TESTIMONIAL ====================-->
    <section class="testimonial section">
        <h2 class="section__title">Testimonial</h2>  
        <span class="section__subtitle">My client saying</span>

        <div class="testimonial_container container swiper-container">
        <div class="swiper-wrapper">
            <!--========= TESTIMONIAL 1 ==========-->
            <div class="testimonial_content swiper-slide">
                <div class="testimonial_data">
                    <div class="testimonial_header">
                        <img src="<?php echo e(asset('assets/img/testimonial1.jpeg')); ?>" alt="" class="testimonial_img">

                        <div>
                            <h3 class="testimonial_name">Jay Smith</h3>
                            <span class="testimonial_client">Client</span>
                        </div>
                    </div>

                    <div>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                    </div>
                </div>
                <p class="testimonial_description">
                    I get a good impression,I carry out my project with all the possible
                    quality and attention and support 24 hours a day.
                </p>
            </div>
            <!--========= TESTIMONIAL 2 ==========-->
            <div class="testimonial_content swiper-slide">
                <div class="testimonial_data">
                    <div class="testimonial_header">
                        <img src="<?php echo e(asset('assets/img/testimonial2.jpg')); ?>" alt="" class="testimonial_img">

                        <div>
                            <h3 class="testimonial_name">John Smith</h3>
                            <span class="testimonial_client">Client</span>
                        </div>
                    </div>
                    
                    <div>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                    </div>
                </div>
                <p class="testimonial_description">
                    I get a good impression,I carry out my project with all the possible
                    quality and attention and support 24 hours a day.
                </p>
            </div>
            <!--========= TESTIMONIAL 3 ==========-->
            <div class="testimonial_content swiper-slide">
                <div class="testimonial_data">
                    <div class="testimonial_header">
                        <img src="<?php echo e(asset('assets/img/testimonial3.jpeg')); ?>" alt="" class="testimonial_img">

                        <div>
                            <h3 class="testimonial_name">Mike Smith</h3>
                            <span class="testimonial_client">Client</span>
                        </div>
                    </div>
                    
                    <div>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                        <i class="uil uil-star testimonial_icon-star"></i>
                    </div>
                </div>
                <p class="testimonial_description">
                    I get a good impression,I carry out my project with all the possible
                    quality and attention and support 24 hours a day.
                </p>
            </div>
            
        </div>
        <!--Add Pagination-->
        <div class="swiper-pagination swiper-pagination-testimonial"></div>
        </div>
    </section>

    <!--==================== CONTACT ME ====================-->
    <section class="contact section" id="contact">
        <h2 class="section__title">Contact Me</h2>
        <span class="section__subtitle">Get in touch</span>

        <div class="contact_container container grid">
        <div>
            <div class="contact_information">
                <i class="uil uil-phone contact_icon"></i>

                <div>
                    <h3 class="contact_title">Call Me</h3>
                    <span class="contact_subtitle">444-444-444</span>
                </div>
            </div>
            <div class="contact_information">
                <i class="uil uil-envelope contact_icon"></i>

                <div>
                    <h3 class="contact_title">Email</h3>
                    <span class="contact_subtitle"><EMAIL></span>
                </div>
            </div>
            <div class="contact_information">
                <i class="uil uil-map-marker contact_icon"></i>

                <div>
                    <h3 class="contact_title">Location</h3>
                    <span class="contact_subtitle">Germany-Munich Av.munich #1234</span>
                </div>
            </div>
        </div>

        <form action="" class="contact_form grid">
            <div class="contact_inputs grid">
                <div class="contact_content">
                    <label for="" class="contact_label">Name</label>
                    <input type="text" class="contact_input">
                </div>
                <div class="contact_content">
                    <label for="" class="contact_label">Email</label>
                    <input type="email" class="contact_input">
                </div>
            </div>
            <div class="contact_content">
                <label for="" class="contact_label">Project</label>
                <input type="tetx" class="contact_input">
            </div>
            <div class="contact_content">
                <label for="" class="contact_label">Project description</label>
                <textarea name="" id="" cols="0" rows="7" class="contact_input"></textarea>
            </div>
            <div>
                <a href="#" class="button button--flex">
                    Send Message 
                    <i class="uil uil-message button_icon"></i>
                </a>
            </div>
        </form>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.pages.base', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\inetpub\wwwroot\Laravel-Portfolio\resources\views/pages/home/<USER>/ ?>